"""
Strategy Iteration 4: Advanced Risk Management
Improvements over V3:
- Dynamic position sizing based on volatility and drawdown
- Drawdown protection mechanisms
- Time-based stop losses
- Profit-taking rules with scaling out
- Portfolio heat management
- Kelly Criterion position sizing
"""

from backtesting import Backtest, Strategy
from backtesting.lib import crossover, TrailingStrategy
import pandas as pd
import numpy as np
import talib
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
from data.data_loader import load_doge_data


class AdvancedRiskManagementStrategy(TrailingStrategy):
    """
    Strategy with advanced risk management and position sizing
    """
    
    # Moving average parameters
    n1 = 25  # Fast MA
    n2 = 60  # Slow MA
    ma_type = 'SMA'
    
    # Entry parameters
    base_gap = 0.001
    volatility_multiplier = 1.0
    min_hold_periods = 2
    
    # Technical indicator parameters
    rsi_period = 14
    bb_period = 20
    bb_std = 2.0
    
    # Risk management parameters
    max_portfolio_risk = 0.02  # Maximum 2% portfolio risk per trade
    max_total_exposure = 0.10  # Maximum 10% total portfolio exposure
    drawdown_threshold = 0.05  # 5% drawdown triggers risk reduction
    kelly_lookback = 50        # Lookback for <PERSON> calculation
    
    # Position sizing parameters
    base_position_size = 0.05  # Base 5% position size
    max_position_size = 0.15   # Maximum 15% position size
    volatility_target = 0.02   # Target 2% volatility per position
    
    # Stop loss and profit taking
    initial_stop_atr = 2.0     # Initial stop loss in ATR
    trailing_stop_atr = 1.5    # Trailing stop in ATR
    profit_target_1 = 0.03     # First profit target at 3%
    profit_target_2 = 0.06     # Second profit target at 6%
    scale_out_1 = 0.5          # Scale out 50% at first target
    scale_out_2 = 0.3          # Scale out 30% at second target
    
    def init(self):
        super().init()
        
        # Moving averages
        if self.ma_type == 'SMA':
            self.ma_fast = self.I(talib.SMA, self.data.Close, self.n1)
            self.ma_slow = self.I(talib.SMA, self.data.Close, self.n2)
        else:  # EMA
            self.ma_fast = self.I(talib.EMA, self.data.Close, self.n1)
            self.ma_slow = self.I(talib.EMA, self.data.Close, self.n2)
            
        # Technical indicators
        self.rsi = self.I(talib.RSI, self.data.Close, self.rsi_period)
        
        # MACD
        macd_line, macd_signal, macd_hist = talib.MACD(self.data.Close, 12, 26, 9)
        self.macd = self.I(lambda: macd_line)
        self.macd_signal = self.I(lambda: macd_signal)
        self.macd_hist = self.I(lambda: macd_hist)
        
        # Bollinger Bands
        bb_upper, bb_middle, bb_lower = talib.BBANDS(self.data.Close, self.bb_period, self.bb_std, self.bb_std)
        self.bb_upper = self.I(lambda: bb_upper)
        self.bb_middle = self.I(lambda: bb_middle)
        self.bb_lower = self.I(lambda: bb_lower)
        
        # ATR for position sizing and stops
        self.atr = self.I(talib.ATR, self.data.High, self.data.Low, self.data.Close, 14)
        
        # Volatility measures
        self.volatility = self.I(self._calculate_volatility, self.data.Close, 20)
        self.returns = self.I(self._calculate_returns, self.data.Close)
        
        # Track position and risk metrics
        self.entry_bar = 0
        self.entry_price = 0
        self.initial_stop = 0
        self.profit_targets_hit = 0
        self.peak_equity = 100  # Starting equity
        self.max_drawdown = 0
        
        # Kelly Criterion tracking
        self.trade_returns = []
        
        # Set basic trailing stop
        self.set_atr_periods(14)
        self.set_trailing_sl(self.trailing_stop_atr)
        
    def _calculate_volatility(self, close, period):
        """Calculate rolling volatility"""
        returns = pd.Series(close).pct_change()
        return returns.rolling(window=period).std() * np.sqrt(period)
    
    def _calculate_returns(self, close):
        """Calculate returns for Kelly Criterion"""
        return pd.Series(close).pct_change()
    
    def _get_current_drawdown(self):
        """Calculate current drawdown from peak equity"""
        current_equity = self.equity
        if current_equity > self.peak_equity:
            self.peak_equity = current_equity
            
        drawdown = (self.peak_equity - current_equity) / self.peak_equity
        self.max_drawdown = max(self.max_drawdown, drawdown)
        return drawdown
    
    def _calculate_kelly_fraction(self):
        """Calculate Kelly Criterion position size"""
        if len(self.trade_returns) < 10:
            return self.base_position_size
            
        recent_returns = self.trade_returns[-self.kelly_lookback:]
        
        if len(recent_returns) < 5:
            return self.base_position_size
            
        # Calculate win rate and average win/loss
        wins = [r for r in recent_returns if r > 0]
        losses = [r for r in recent_returns if r < 0]
        
        if not wins or not losses:
            return self.base_position_size
            
        win_rate = len(wins) / len(recent_returns)
        avg_win = np.mean(wins)
        avg_loss = abs(np.mean(losses))
        
        # Kelly formula: f = (bp - q) / b
        # where b = avg_win/avg_loss, p = win_rate, q = 1-win_rate
        if avg_loss == 0:
            return self.base_position_size
            
        b = avg_win / avg_loss
        kelly_fraction = (b * win_rate - (1 - win_rate)) / b
        
        # Cap Kelly fraction to reasonable bounds
        kelly_fraction = max(0, min(kelly_fraction, 0.25))
        
        return kelly_fraction
    
    def _calculate_volatility_adjusted_size(self, signal_strength):
        """Calculate position size based on volatility targeting"""
        if len(self.atr) == 0 or pd.isna(self.atr[-1]):
            return self.base_position_size
            
        # Current price volatility (ATR as % of price)
        current_vol = self.atr[-1] / self.data.Close[-1]
        
        # Adjust position size to target volatility
        vol_adjusted_size = self.volatility_target / current_vol
        
        # Apply signal strength multiplier
        size = vol_adjusted_size * signal_strength
        
        # Apply Kelly Criterion
        kelly_size = self._calculate_kelly_fraction()
        size = min(size, kelly_size)
        
        # Apply drawdown protection
        current_drawdown = self._get_current_drawdown()
        if current_drawdown > self.drawdown_threshold:
            drawdown_multiplier = max(0.3, 1 - (current_drawdown * 2))
            size *= drawdown_multiplier
        
        # Ensure within bounds
        return max(0.01, min(size, self.max_position_size))
    
    def _get_signal_strength(self, direction):
        """Calculate signal strength (0-1)"""
        strength = 0.0
        
        # MA momentum
        if len(self.ma_fast) >= 2:
            ma_momentum = (self.ma_fast[-1] - self.ma_fast[-2]) / self.ma_fast[-2]
            if (direction > 0 and ma_momentum > 0) or (direction < 0 and ma_momentum < 0):
                strength += 0.3
        
        # RSI confirmation
        if len(self.rsi) > 0 and not pd.isna(self.rsi[-1]):
            rsi_val = self.rsi[-1]
            if direction > 0 and 30 < rsi_val < 65:
                strength += 0.25
            elif direction < 0 and 35 < rsi_val < 70:
                strength += 0.25
        
        # MACD confirmation
        if (len(self.macd_hist) >= 2 and 
            not pd.isna(self.macd_hist[-1]) and not pd.isna(self.macd_hist[-2])):
            macd_momentum = self.macd_hist[-1] - self.macd_hist[-2]
            if (direction > 0 and macd_momentum > 0) or (direction < 0 and macd_momentum < 0):
                strength += 0.25
        
        # Bollinger Bands
        if (len(self.bb_upper) > 0 and len(self.bb_lower) > 0 and
            not pd.isna(self.bb_upper[-1]) and not pd.isna(self.bb_lower[-1])):
            price = self.data.Close[-1]
            bb_position = (price - self.bb_lower[-1]) / (self.bb_upper[-1] - self.bb_lower[-1])
            
            if direction > 0 and bb_position < 0.3:  # Near lower band for long
                strength += 0.2
            elif direction < 0 and bb_position > 0.7:  # Near upper band for short
                strength += 0.2
        
        return min(strength, 1.0)
    
    def _should_scale_out(self):
        """Check if we should scale out of position"""
        if self.position.size == 0 or self.entry_price == 0:
            return False, 0
            
        current_price = self.data.Close[-1]
        
        if self.position.size > 0:  # Long position
            profit_pct = (current_price - self.entry_price) / self.entry_price
            
            if profit_pct >= self.profit_target_2 and self.profit_targets_hit < 2:
                return True, self.scale_out_2
            elif profit_pct >= self.profit_target_1 and self.profit_targets_hit < 1:
                return True, self.scale_out_1
                
        else:  # Short position
            profit_pct = (self.entry_price - current_price) / self.entry_price
            
            if profit_pct >= self.profit_target_2 and self.profit_targets_hit < 2:
                return True, self.scale_out_2
            elif profit_pct >= self.profit_target_1 and self.profit_targets_hit < 1:
                return True, self.scale_out_1
        
        return False, 0
    
    def _update_stops(self):
        """Update stop loss levels"""
        if self.position.size == 0 or len(self.atr) == 0:
            return
            
        current_price = self.data.Close[-1]
        atr_value = self.atr[-1]
        
        if self.position.size > 0:  # Long position
            # Initial stop loss
            if self.initial_stop == 0:
                self.initial_stop = current_price - (atr_value * self.initial_stop_atr)
            
            # Trailing stop (only move up)
            new_trailing_stop = current_price - (atr_value * self.trailing_stop_atr)
            if new_trailing_stop > self.initial_stop:
                self.initial_stop = new_trailing_stop
                
        else:  # Short position
            # Initial stop loss
            if self.initial_stop == 0:
                self.initial_stop = current_price + (atr_value * self.initial_stop_atr)
            
            # Trailing stop (only move down)
            new_trailing_stop = current_price + (atr_value * self.trailing_stop_atr)
            if new_trailing_stop < self.initial_stop:
                self.initial_stop = new_trailing_stop
    
    def next(self):
        super().next()
        
        # Skip if not enough data
        if len(self.ma_fast) < 2 or len(self.ma_slow) < 2:
            return
        
        # Update stops for existing position
        self._update_stops()
        
        # Simplified profit taking - close entire position at profit targets
        if self.position.size != 0 and self.entry_price != 0:
            current_price = self.data.Close[-1]

            if self.position.size > 0:  # Long position
                profit_pct = (current_price - self.entry_price) / self.entry_price
                if profit_pct >= self.profit_target_2:  # Take profit at 6%
                    trade_return = profit_pct
                    self.trade_returns.append(trade_return)
                    self.position.close()
                    self.initial_stop = 0
                    self.profit_targets_hit = 0
                    return

            else:  # Short position
                profit_pct = (self.entry_price - current_price) / self.entry_price
                if profit_pct >= self.profit_target_2:  # Take profit at 6%
                    trade_return = profit_pct
                    self.trade_returns.append(trade_return)
                    self.position.close()
                    self.initial_stop = 0
                    self.profit_targets_hit = 0
                    return
        
        # Check stop loss
        if self.position.size != 0 and self.initial_stop != 0:
            current_price = self.data.Close[-1]
            
            if ((self.position.size > 0 and current_price <= self.initial_stop) or
                (self.position.size < 0 and current_price >= self.initial_stop)):
                
                # Record trade return for Kelly calculation
                if self.entry_price != 0:
                    if self.position.size > 0:
                        trade_return = (current_price - self.entry_price) / self.entry_price
                    else:
                        trade_return = (self.entry_price - current_price) / self.entry_price
                    self.trade_returns.append(trade_return)
                
                self.position.close()
                self.initial_stop = 0
                self.profit_targets_hit = 0
                return
        
        # Entry signals
        gap = self.base_gap + (self.volatility[-1] * self.volatility_multiplier if len(self.volatility) > 0 and not pd.isna(self.volatility[-1]) else 0)
        ma_ratio = self.ma_fast[-1] / self.ma_slow[-1]
        
        # Long signal
        long_signal = ma_ratio > 1 + gap
        long_strength = self._get_signal_strength(1) if long_signal else 0
        
        # Short signal
        short_signal = ma_ratio < 1 - gap
        short_strength = self._get_signal_strength(-1) if short_signal else 0
        
        # Minimum signal strength
        min_strength = 0.5
        
        # Entry logic
        if (self.position.size <= 0 and long_signal and long_strength >= min_strength):
            if self.position.size < 0:
                # Record trade return
                if self.entry_price != 0:
                    trade_return = (self.entry_price - self.data.Close[-1]) / self.entry_price
                    self.trade_returns.append(trade_return)

            self.position.close()
            size = self._calculate_volatility_adjusted_size(long_strength)
            size = max(0.01, min(size, 0.95))  # Ensure valid size
            self.buy(size=size)
            self.entry_bar = len(self.data)
            self.entry_price = self.data.Close[-1]
            self.initial_stop = 0
            self.profit_targets_hit = 0

        elif (self.position.size >= 0 and short_signal and short_strength >= min_strength):
            if self.position.size > 0:
                # Record trade return
                if self.entry_price != 0:
                    trade_return = (self.data.Close[-1] - self.entry_price) / self.entry_price
                    self.trade_returns.append(trade_return)

            self.position.close()
            size = self._calculate_volatility_adjusted_size(short_strength)
            size = max(0.01, min(size, 0.95))  # Ensure valid size
            self.sell(size=size)
            self.entry_bar = len(self.data)
            self.entry_price = self.data.Close[-1]
            self.initial_stop = 0
            self.profit_targets_hit = 0


def run_strategy_v4(timeframe='15m', months=None, optimize=True):
    """Run the advanced risk management strategy"""
    if months is None:
        months = ['2024-01', '2024-02', '2024-03', '2024-04', 
                 '2024-05', '2024-06', '2024-07', '2024-08']
    
    print(f"Loading {timeframe} data for Advanced Risk Management Strategy...")
    data = load_doge_data([timeframe], months, add_indicators=False)
    df = data[timeframe]
    
    print(f"Data loaded: {len(df)} records from {df.index.min()} to {df.index.max()}")
    
    # Run backtest
    bt = Backtest(df, AdvancedRiskManagementStrategy, cash=100, commission=0.0003)
    
    print("\n=== Advanced Risk Management Strategy - Default Parameters ===")
    stats = bt.run()
    print(stats)
    
    if optimize:
        print("\n=== Running Parameter Optimization ===")
        
        stats_opt = bt.optimize(
            n1=range(20, 35, 5),
            n2=range(50, 80, 10),
            ma_type=['SMA', 'EMA'],
            base_gap=[0.0005, 0.001, 0.002],
            max_portfolio_risk=[0.015, 0.02, 0.025],
            drawdown_threshold=[0.03, 0.05, 0.07],
            base_position_size=[0.03, 0.05, 0.07],
            initial_stop_atr=[1.5, 2.0, 2.5],
            profit_target_1=[0.02, 0.03, 0.04],
            maximize='Sharpe Ratio',
            constraint=lambda p: p.n1 < p.n2,
            max_tries=80
        )
        
        print("\n=== Optimized Results ===")
        print(stats_opt)
        print(f"\nOptimal parameters: {stats_opt['_strategy']}")
        
        return stats_opt
    
    return stats


if __name__ == "__main__":
    results = run_strategy_v4(timeframe='15m', optimize=True)
