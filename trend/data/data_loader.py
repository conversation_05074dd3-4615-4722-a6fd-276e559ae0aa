"""
Multi-timeframe data loading and processing for DOGE cryptocurrency trading
"""
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple
import warnings
warnings.simplefilter(action='ignore', category=FutureWarning)


class MultiTimeframeDataLoader:
    """
    Handles loading and synchronizing DOGE data across multiple timeframes
    """
    
    def __init__(self, base_path: str = '/Users/<USER>/workspace/data/DOGE'):
        self.base_path = base_path
        self.timeframes = {
            '15m': '15m',
            '30m': '30m', 
            '1h': '1h',
            '4h': '4h'
        }
        
    def load_single_timeframe(self, timeframe: str, months: List[str], 
                            head: int = 200000, tail: int = 200000) -> pd.DataFrame:
        """
        Load data for a single timeframe
        
        Args:
            timeframe: Timeframe identifier (15m, 30m, 1h, 4h)
            months: List of month strings (e.g., ['2024-01', '2024-02'])
            head: Number of records to take from start
            tail: Number of records to take from end after head
            
        Returns:
            DataFrame with OHLCV data
        """
        dfs = []
        for month in months:
            try:
                path = f'{self.base_path}/DOGE_{month}.parquet'
                df = pd.read_parquet(path)
                # Filter for specific timeframe
                df = df[df['i'] == timeframe]
                dfs.append(df)
            except FileNotFoundError:
                print(f"Warning: File not found for {month}")
                continue
                
        if not dfs:
            raise ValueError(f"No data found for timeframe {timeframe}")
            
        # Combine all months
        df = pd.concat(dfs, ignore_index=True)
        
        # Sort by timestamp and remove duplicates
        df = df.sort_values(by='z', ascending=True)
        df = df.drop_duplicates(subset=['z'])
        
        # Apply head/tail filtering
        df = df.head(head).tail(tail)
        
        # Convert timestamp and set as index
        df['datetime'] = pd.to_datetime(df['z'])
        df = df.set_index('datetime')
        
        # Rename columns to standard format
        df = df.rename(columns={
            'o': 'Open',
            'h': 'High', 
            'l': 'Low',
            'c': 'Close',
            'v': 'Volume',
            'q': 'Qty'
        })
        
        # Select only OHLCV columns
        df = df[['Open', 'High', 'Low', 'Close', 'Volume', 'Qty']]
        
        return df
    
    def load_multiple_timeframes(self, timeframes: List[str], months: List[str],
                               head: int = 200000, tail: int = 200000) -> Dict[str, pd.DataFrame]:
        """
        Load data for multiple timeframes
        
        Args:
            timeframes: List of timeframe identifiers
            months: List of month strings
            head: Number of records to take from start
            tail: Number of records to take from end
            
        Returns:
            Dictionary mapping timeframe to DataFrame
        """
        data = {}
        for tf in timeframes:
            print(f"Loading {tf} data...")
            data[tf] = self.load_single_timeframe(tf, months, head, tail)
            print(f"Loaded {len(data[tf])} records for {tf}")
            
        return data
    
    def resample_to_higher_timeframe(self, df: pd.DataFrame, target_timeframe: str) -> pd.DataFrame:
        """
        Resample lower timeframe data to higher timeframe
        
        Args:
            df: Source DataFrame with OHLCV data
            target_timeframe: Target timeframe (30T, 1H, 4H, etc.)
            
        Returns:
            Resampled DataFrame
        """
        # Define resampling rules
        agg_rules = {
            'Open': 'first',
            'High': 'max',
            'Low': 'min', 
            'Close': 'last',
            'Volume': 'sum',
            'Qty': 'sum'
        }
        
        # Resample data
        resampled = df.resample(target_timeframe).agg(agg_rules)
        
        # Remove rows with NaN values
        resampled = resampled.dropna()
        
        return resampled
    
    def synchronize_timeframes(self, data: Dict[str, pd.DataFrame]) -> Dict[str, pd.DataFrame]:
        """
        Synchronize multiple timeframes to have aligned timestamps
        
        Args:
            data: Dictionary of timeframe DataFrames
            
        Returns:
            Dictionary of synchronized DataFrames
        """
        # Find common time range across all timeframes
        start_times = [df.index.min() for df in data.values()]
        end_times = [df.index.max() for df in data.values()]
        
        common_start = max(start_times)
        common_end = min(end_times)
        
        print(f"Common time range: {common_start} to {common_end}")
        
        # Filter all timeframes to common range
        synchronized_data = {}
        for tf, df in data.items():
            synchronized_data[tf] = df[(df.index >= common_start) & (df.index <= common_end)]
            print(f"Synchronized {tf}: {len(synchronized_data[tf])} records")
            
        return synchronized_data
    
    def add_technical_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Add basic technical indicators to DataFrame
        
        Args:
            df: OHLCV DataFrame
            
        Returns:
            DataFrame with additional technical indicators
        """
        try:
            import talib
            
            # Moving averages
            df['SMA_20'] = talib.SMA(df['Close'], timeperiod=20)
            df['EMA_20'] = talib.EMA(df['Close'], timeperiod=20)
            df['SMA_50'] = talib.SMA(df['Close'], timeperiod=50)
            df['EMA_50'] = talib.EMA(df['Close'], timeperiod=50)
            
            # Momentum indicators
            df['RSI'] = talib.RSI(df['Close'], timeperiod=14)
            macd, macd_signal, macd_hist = talib.MACD(df['Close'])
            df['MACD'] = macd
            df['MACD_Signal'] = macd_signal
            df['MACD_Hist'] = macd_hist
            
            # Volatility indicators
            df['ATR'] = talib.ATR(df['High'], df['Low'], df['Close'], timeperiod=14)
            df['BB_Upper'], df['BB_Middle'], df['BB_Lower'] = talib.BBANDS(df['Close'])
            
            # Trend indicators
            df['ADX'] = talib.ADX(df['High'], df['Low'], df['Close'], timeperiod=14)
            
        except ImportError:
            print("Warning: talib not available, skipping technical indicators")
            
        return df


def load_doge_data(timeframes: List[str] = ['15m', '30m', '1h'], 
                  months: List[str] = None,
                  add_indicators: bool = True) -> Dict[str, pd.DataFrame]:
    """
    Convenience function to load DOGE data for multiple timeframes
    
    Args:
        timeframes: List of timeframes to load
        months: List of months to load (defaults to 2024 data)
        add_indicators: Whether to add technical indicators
        
    Returns:
        Dictionary of synchronized DataFrames
    """
    if months is None:
        months = ['2024-01', '2024-02', '2024-03', '2024-04', 
                 '2024-05', '2024-06', '2024-07', '2024-08']
    
    loader = MultiTimeframeDataLoader()
    
    # Load data for all timeframes
    data = loader.load_multiple_timeframes(timeframes, months)
    
    # Synchronize timeframes
    data = loader.synchronize_timeframes(data)
    
    # Add technical indicators if requested
    if add_indicators:
        for tf in timeframes:
            data[tf] = loader.add_technical_indicators(data[tf])
    
    return data


if __name__ == "__main__":
    # Test the data loader
    print("Testing multi-timeframe data loader...")
    
    # Load data for multiple timeframes
    data = load_doge_data(['15m', '30m', '1h'])
    
    # Print summary statistics
    for tf, df in data.items():
        print(f"\n{tf} timeframe:")
        print(f"  Records: {len(df)}")
        print(f"  Date range: {df.index.min()} to {df.index.max()}")
        print(f"  Columns: {list(df.columns)}")
        print(f"  Sample data:\n{df.head(2)}")
