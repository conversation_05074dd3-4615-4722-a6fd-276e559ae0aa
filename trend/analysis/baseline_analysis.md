# Baseline Strategy Analysis - DOGE Dual Moving Average

## Current Strategy Overview
The current implementation uses a dual moving average crossover strategy with the following characteristics:

### Strategy Components
- **Primary Timeframe**: 15-minute candlesticks
- **Secondary Timeframe**: 30-minute resampled data
- **Moving Averages**: 
  - Fast EMA (15m): 21 periods
  - Slow SMA (15m): 76 periods
  - Fast EMA (30m): 7 periods  
  - Slow SMA (30m): 20 periods
- **Additional Indicators**: RSI, MACD, KAMA, ATR
- **Risk Management**: Trailing stop-loss (2x ATR)

### Entry/Exit Logic
- **Long Entry**: Fast MA / Slow MA > 1 + gap (0.001)
- **Short Entry**: Fast MA / Slow MA < 1 - gap (0.001)
- **Position Management**: Close existing position before opening new one

## Baseline Performance Metrics (2024 Data)

### Initial Run (Default Parameters)
- **Return**: -53.44%
- **Annual Return**: -67.98%
- **Max Drawdown**: -63.99%
- **Sharpe Ratio**: -1.70
- **Win Rate**: 38.27%
- **Number of Trades**: 1,526
- **Profit Factor**: 1.05

### Optimized Parameters (n1=37, n2=53)
- **Return**: -22.58%
- **Annual Return**: -32.45%
- **Max Drawdown**: -63.86%
- **Sharpe Ratio**: -0.35
- **Win Rate**: 39.83%
- **Number of Trades**: 1,436
- **Profit Factor**: 1.11

## Key Issues Identified

### 1. Poor Risk-Adjusted Returns
- Negative Sharpe ratios indicate poor risk-adjusted performance
- High volatility (91.45% annualized) relative to returns

### 2. Excessive Drawdown
- Maximum drawdown of ~64% is unacceptable for target <15%
- Long drawdown periods (140+ days)

### 3. Low Win Rate
- Win rate around 39-40% suggests poor signal quality
- Need better entry/exit timing

### 4. Over-Trading
- 1,400+ trades in 8 months suggests excessive trading
- High commission costs ($68.47) eating into profits

### 5. Market Regime Insensitivity
- Strategy doesn't adapt to different market conditions
- No trend strength or volatility filtering

## Performance Targets
- **Annual Return**: >50%
- **Maximum Drawdown**: <15%
- **Sharpe Ratio**: >1.5 (target)
- **Win Rate**: >50% (target)

## Next Steps
1. Implement multi-timeframe confirmation
2. Add volatility and trend strength filters
3. Improve risk management with dynamic position sizing
4. Reduce trading frequency with better signal quality
5. Add market regime detection
