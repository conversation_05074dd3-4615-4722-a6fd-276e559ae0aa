"""
Strategy Iteration 1: Enhanced Dual Moving Average
Improvements over baseline:
- Dynamic gap thresholds based on volatility
- Minimum holding period to reduce over-trading
- Better position sizing
- Multiple MA type testing
"""

from backtesting import Backtest, Strategy
from backtesting.lib import crossover, TrailingStrategy
import pandas as pd
import numpy as np
import talib
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
from data.data_loader import load_doge_data


class EnhancedDualMA(TrailingStrategy):
    """
    Enhanced dual moving average strategy with improved risk management
    """
    
    # Strategy parameters for optimization
    n1 = 21  # Fast MA period
    n2 = 76  # Slow MA period
    ma_type = 'EMA'  # MA type: SMA, EMA, WMA
    base_gap = 0.001  # Base gap threshold
    volatility_multiplier = 2.0  # Multiplier for volatility-based gap
    min_hold_periods = 3  # Minimum periods to hold position
    position_size = 0.95  # Fraction of equity to use per trade
    
    def init(self):
        super().init()
        
        # Calculate moving averages based on type
        if self.ma_type == 'SMA':
            self.ma_fast = self.I(talib.SMA, self.data.Close, self.n1)
            self.ma_slow = self.I(talib.SMA, self.data.Close, self.n2)
        elif self.ma_type == 'EMA':
            self.ma_fast = self.I(talib.EMA, self.data.Close, self.n1)
            self.ma_slow = self.I(talib.EMA, self.data.Close, self.n2)
        elif self.ma_type == 'WMA':
            self.ma_fast = self.I(talib.WMA, self.data.Close, self.n1)
            self.ma_slow = self.I(talib.WMA, self.data.Close, self.n2)
        else:  # Default to EMA
            self.ma_fast = self.I(talib.EMA, self.data.Close, self.n1)
            self.ma_slow = self.I(talib.EMA, self.data.Close, self.n2)
            
        # Technical indicators for signal enhancement
        self.rsi = self.I(talib.RSI, self.data.Close, 14)
        self.atr = self.I(talib.ATR, self.data.High, self.data.Low, self.data.Close, 14)
        
        # Volatility measure for dynamic gap
        self.volatility = self.I(self._calculate_volatility, self.data.Close, 20)
        
        # Track position entry time for minimum hold period
        self.entry_bar = 0
        
        # Set trailing stop parameters
        self.set_atr_periods(14)
        self.set_trailing_sl(2.0)
        
    def _calculate_volatility(self, close, period):
        """Calculate rolling volatility"""
        returns = pd.Series(close).pct_change()
        return returns.rolling(window=period).std() * np.sqrt(period)
    
    def _get_dynamic_gap(self):
        """Calculate dynamic gap based on current volatility"""
        if len(self.volatility) == 0 or pd.isna(self.volatility[-1]):
            return self.base_gap
        
        current_vol = self.volatility[-1]
        # Scale gap by volatility - higher volatility requires larger gap
        dynamic_gap = self.base_gap + (current_vol * self.volatility_multiplier)
        
        # Cap the gap to reasonable bounds
        return min(max(dynamic_gap, self.base_gap), 0.01)  # Between 0.1% and 1%
    
    def _can_exit_position(self):
        """Check if minimum holding period has passed"""
        return len(self.data) - self.entry_bar >= self.min_hold_periods
    
    def _get_position_size(self):
        """Calculate position size based on available equity"""
        return self.position_size
    
    def _signal_strength(self):
        """Calculate signal strength based on multiple factors"""
        if len(self.ma_fast) < 2 or len(self.ma_slow) < 2:
            return 0
            
        # MA ratio momentum
        current_ratio = self.ma_fast[-1] / self.ma_slow[-1]
        prev_ratio = self.ma_fast[-2] / self.ma_slow[-2]
        ratio_momentum = current_ratio - prev_ratio
        
        # RSI filter (avoid extreme overbought/oversold)
        rsi_filter = 1.0
        if len(self.rsi) > 0 and not pd.isna(self.rsi[-1]):
            if self.rsi[-1] > 80 or self.rsi[-1] < 20:
                rsi_filter = 0.5  # Reduce signal strength in extreme RSI
                
        return abs(ratio_momentum) * rsi_filter
    
    def next(self):
        super().next()
        
        # Skip if not enough data
        if len(self.ma_fast) < 2 or len(self.ma_slow) < 2:
            return
            
        # Calculate dynamic gap and signal strength
        gap = self._get_dynamic_gap()
        signal_strength = self._signal_strength()
        
        # Minimum signal strength threshold
        if signal_strength < 0.0001:
            return
            
        # Calculate MA ratio
        ma_ratio = self.ma_fast[-1] / self.ma_slow[-1]
        
        # Long entry conditions
        if (self.position.size <= 0 and 
            ma_ratio > 1 + gap and
            len(self.rsi) > 0 and self.rsi[-1] < 75):  # Not too overbought
            
            self.position.close()
            size = self._get_position_size()
            self.buy(size=size)
            self.entry_bar = len(self.data)
            
        # Short entry conditions  
        elif (self.position.size >= 0 and 
              ma_ratio < 1 - gap and
              len(self.rsi) > 0 and self.rsi[-1] > 25):  # Not too oversold
            
            self.position.close()
            size = self._get_position_size()
            self.sell(size=size)
            self.entry_bar = len(self.data)
            
        # Early exit conditions (override minimum hold if strong opposing signal)
        elif self.position.size != 0 and self._can_exit_position():
            
            # Exit long if strong bearish signal
            if (self.position.size > 0 and 
                ma_ratio < 1 - (gap * 2) and  # Stronger opposing signal
                signal_strength > 0.001):
                self.position.close()
                
            # Exit short if strong bullish signal
            elif (self.position.size < 0 and 
                  ma_ratio > 1 + (gap * 2) and  # Stronger opposing signal
                  signal_strength > 0.001):
                self.position.close()


def run_strategy_v1(timeframe='15m', months=None, optimize=True):
    """
    Run the enhanced dual MA strategy
    
    Args:
        timeframe: Data timeframe to use
        months: List of months to test
        optimize: Whether to run optimization
    """
    if months is None:
        months = ['2024-01', '2024-02', '2024-03', '2024-04', 
                 '2024-05', '2024-06', '2024-07', '2024-08']
    
    print(f"Loading {timeframe} data for Enhanced Dual MA Strategy...")
    data = load_doge_data([timeframe], months, add_indicators=False)
    df = data[timeframe]
    
    print(f"Data loaded: {len(df)} records from {df.index.min()} to {df.index.max()}")
    
    # Run backtest with default parameters
    bt = Backtest(df, EnhancedDualMA, cash=100, commission=0.0003)
    
    print("\n=== Enhanced Dual MA Strategy - Default Parameters ===")
    stats = bt.run()
    print(stats)
    
    if optimize:
        print("\n=== Running Parameter Optimization ===")
        
        # Optimization with constraints
        stats_opt = bt.optimize(
            n1=range(10, 50, 5),
            n2=range(30, 100, 10),
            ma_type=['SMA', 'EMA', 'WMA'],
            base_gap=[0.0005, 0.001, 0.002, 0.003],
            volatility_multiplier=[1.0, 1.5, 2.0, 2.5],
            min_hold_periods=[1, 2, 3, 5],
            position_size=[0.8, 0.9, 0.95, 1.0],
            maximize='Sharpe Ratio',
            constraint=lambda p: p.n1 < p.n2,
            max_tries=200
        )
        
        print("\n=== Optimized Results ===")
        print(stats_opt)
        print(f"\nOptimal parameters: {stats_opt['_strategy']}")
        
        return stats_opt
    
    return stats


if __name__ == "__main__":
    # Test the enhanced strategy
    results = run_strategy_v1(timeframe='15m', optimize=True)
