"""
Strategy Iteration 3: Technical Indicators Integration
Improvements over V2:
- RSI divergence detection for entry timing
- MACD histogram for momentum confirmation
- Bollinger Bands for volatility-based entries
- Volume analysis for signal strength
- ATR-based dynamic position sizing
- Better balance between selectivity and opportunity
"""

from backtesting import Backtest, Strategy
from backtesting.lib import crossover, TrailingStrategy, resample_apply
import pandas as pd
import numpy as np
import talib
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
from data.data_loader import load_doge_data


class TechnicalIndicatorsStrategy(TrailingStrategy):
    """
    Strategy with comprehensive technical indicator integration
    """
    
    # Moving average parameters
    n1 = 20  # Fast MA
    n2 = 50  # Slow MA
    ma_type = 'EMA'
    
    # Entry parameters
    base_gap = 0.001
    volatility_multiplier = 1.5
    min_hold_periods = 2
    
    # Technical indicator parameters
    rsi_period = 14
    rsi_oversold = 30
    rsi_overbought = 70
    bb_period = 20
    bb_std = 2.0
    volume_ma_period = 20
    
    # Position sizing parameters
    base_position_size = 0.8
    max_position_size = 1.0
    atr_multiplier = 2.0
    
    def init(self):
        super().init()
        
        # Moving averages
        if self.ma_type == 'SMA':
            self.ma_fast = self.I(talib.SMA, self.data.Close, self.n1)
            self.ma_slow = self.I(talib.SMA, self.data.Close, self.n2)
        else:  # EMA
            self.ma_fast = self.I(talib.EMA, self.data.Close, self.n1)
            self.ma_slow = self.I(talib.EMA, self.data.Close, self.n2)
            
        # Technical indicators
        self.rsi = self.I(talib.RSI, self.data.Close, self.rsi_period)
        
        # MACD
        macd_line, macd_signal, macd_hist = talib.MACD(self.data.Close, 12, 26, 9)
        self.macd = self.I(lambda: macd_line)
        self.macd_signal = self.I(lambda: macd_signal)
        self.macd_hist = self.I(lambda: macd_hist)
        
        # Bollinger Bands
        bb_upper, bb_middle, bb_lower = talib.BBANDS(self.data.Close, self.bb_period, self.bb_std, self.bb_std)
        self.bb_upper = self.I(lambda: bb_upper)
        self.bb_middle = self.I(lambda: bb_middle)
        self.bb_lower = self.I(lambda: bb_lower)
        
        # ATR for position sizing and stops
        self.atr = self.I(talib.ATR, self.data.High, self.data.Low, self.data.Close, 14)
        
        # Volume indicators (convert to float64 for talib)
        volume_float = self.I(lambda x: pd.Series(x).astype(np.float64), self.data.Volume)
        self.volume_ma = self.I(talib.SMA, volume_float, self.volume_ma_period)
        
        # Volatility
        self.volatility = self.I(self._calculate_volatility, self.data.Close, 20)
        
        # Track positions
        self.entry_bar = 0
        self.entry_price = 0
        
        # Set trailing stop
        self.set_atr_periods(14)
        self.set_trailing_sl(self.atr_multiplier)
        
    def _calculate_volatility(self, close, period):
        """Calculate rolling volatility"""
        returns = pd.Series(close).pct_change()
        return returns.rolling(window=period).std() * np.sqrt(period)
    
    def _get_dynamic_gap(self):
        """Calculate dynamic gap based on volatility"""
        if len(self.volatility) == 0 or pd.isna(self.volatility[-1]):
            return self.base_gap
        
        current_vol = self.volatility[-1]
        dynamic_gap = self.base_gap + (current_vol * self.volatility_multiplier)
        return min(max(dynamic_gap, self.base_gap), 0.005)
    
    def _detect_rsi_divergence(self, lookback=5):
        """Detect RSI divergence with price"""
        if len(self.rsi) < lookback + 1 or len(self.data.Close) < lookback + 1:
            return 0
        
        # Get recent price and RSI values
        recent_prices = [self.data.Close[i] for i in range(-lookback, 0)]
        recent_rsi = [self.rsi[i] for i in range(-lookback, 0)]
        
        if any(pd.isna(val) for val in recent_rsi):
            return 0
        
        # Check for bullish divergence (price lower low, RSI higher low)
        if (recent_prices[-1] < recent_prices[0] and 
            recent_rsi[-1] > recent_rsi[0] and
            recent_rsi[-1] < 50):
            return 1  # Bullish divergence
            
        # Check for bearish divergence (price higher high, RSI lower high)
        if (recent_prices[-1] > recent_prices[0] and 
            recent_rsi[-1] < recent_rsi[0] and
            recent_rsi[-1] > 50):
            return -1  # Bearish divergence
            
        return 0
    
    def _get_macd_signal(self):
        """Get MACD signal strength"""
        if len(self.macd_hist) < 2:
            return 0
        
        if pd.isna(self.macd_hist[-1]) or pd.isna(self.macd_hist[-2]):
            return 0
            
        # MACD histogram momentum
        hist_momentum = self.macd_hist[-1] - self.macd_hist[-2]
        
        # MACD line vs signal line
        if len(self.macd) > 0 and len(self.macd_signal) > 0:
            if not pd.isna(self.macd[-1]) and not pd.isna(self.macd_signal[-1]):
                macd_diff = self.macd[-1] - self.macd_signal[-1]
                
                # Bullish: MACD above signal and histogram increasing
                if macd_diff > 0 and hist_momentum > 0:
                    return 1
                # Bearish: MACD below signal and histogram decreasing
                elif macd_diff < 0 and hist_momentum < 0:
                    return -1
        
        return 0
    
    def _get_bb_signal(self):
        """Get Bollinger Bands signal"""
        if (len(self.bb_upper) == 0 or len(self.bb_lower) == 0 or 
            pd.isna(self.bb_upper[-1]) or pd.isna(self.bb_lower[-1])):
            return 0
        
        current_price = self.data.Close[-1]
        bb_width = (self.bb_upper[-1] - self.bb_lower[-1]) / self.bb_middle[-1]
        
        # Bollinger Band squeeze (low volatility)
        if bb_width < 0.04:  # Tight bands
            return 0  # Wait for breakout
        
        # Price near lower band (potential bounce)
        if current_price <= self.bb_lower[-1] * 1.01:
            return 1  # Bullish
        
        # Price near upper band (potential reversal)
        if current_price >= self.bb_upper[-1] * 0.99:
            return -1  # Bearish
            
        return 0
    
    def _get_volume_signal(self):
        """Get volume confirmation signal"""
        if len(self.volume_ma) == 0 or pd.isna(self.volume_ma[-1]):
            return 1  # Neutral if no volume data
        
        current_volume = self.data.Volume[-1]
        avg_volume = self.volume_ma[-1]
        
        # High volume confirms signal
        if current_volume > avg_volume * 1.5:
            return 1.5  # Strong confirmation
        elif current_volume > avg_volume * 1.2:
            return 1.2  # Moderate confirmation
        else:
            return 0.8  # Weak volume
    
    def _calculate_position_size(self, signal_strength):
        """Calculate position size based on signal strength and volatility"""
        if len(self.atr) == 0 or pd.isna(self.atr[-1]):
            return self.base_position_size
        
        # Base size adjusted by signal strength
        size = self.base_position_size * signal_strength
        
        # Adjust for volatility (lower size in high volatility)
        if len(self.volatility) > 0 and not pd.isna(self.volatility[-1]):
            vol_adjustment = max(0.5, 1 - (self.volatility[-1] * 10))
            size *= vol_adjustment
        
        return min(size, self.max_position_size)
    
    def _calculate_signal_strength(self, direction):
        """Calculate overall signal strength (0-1)"""
        strength = 0.0
        
        # RSI divergence (strong signal)
        rsi_div = self._detect_rsi_divergence()
        if rsi_div == direction:
            strength += 0.3
        
        # MACD confirmation
        macd_signal = self._get_macd_signal()
        if macd_signal == direction:
            strength += 0.25
        
        # Bollinger Bands
        bb_signal = self._get_bb_signal()
        if bb_signal == direction:
            strength += 0.2
        
        # RSI level (avoid extremes)
        if len(self.rsi) > 0 and not pd.isna(self.rsi[-1]):
            rsi_val = self.rsi[-1]
            if direction > 0 and 25 < rsi_val < 65:  # Good for long
                strength += 0.15
            elif direction < 0 and 35 < rsi_val < 75:  # Good for short
                strength += 0.15
        
        # Volume confirmation
        volume_mult = self._get_volume_signal()
        strength *= volume_mult
        
        return min(strength, 1.0)
    
    def _can_exit_position(self):
        """Check if minimum holding period has passed"""
        return len(self.data) - self.entry_bar >= self.min_hold_periods
    
    def next(self):
        super().next()
        
        # Skip if not enough data
        if len(self.ma_fast) < 2 or len(self.ma_slow) < 2:
            return
        
        # Calculate signals
        gap = self._get_dynamic_gap()
        ma_ratio = self.ma_fast[-1] / self.ma_slow[-1]
        
        # Entry conditions
        long_signal = ma_ratio > 1 + gap
        short_signal = ma_ratio < 1 - gap
        
        # Calculate signal strength
        long_strength = self._calculate_signal_strength(1) if long_signal else 0
        short_strength = self._calculate_signal_strength(-1) if short_signal else 0
        
        # Minimum signal strength threshold
        min_strength = 0.4
        
        # Entry logic
        if self.position.size <= 0 and long_signal and long_strength >= min_strength:
            self.position.close()
            size = self._calculate_position_size(long_strength)
            self.buy(size=size)
            self.entry_bar = len(self.data)
            self.entry_price = self.data.Close[-1]
            
        elif self.position.size >= 0 and short_signal and short_strength >= min_strength:
            self.position.close()
            size = self._calculate_position_size(short_strength)
            self.sell(size=size)
            self.entry_bar = len(self.data)
            self.entry_price = self.data.Close[-1]
            
        # Exit logic
        elif self.position.size != 0 and self._can_exit_position():
            
            # Exit long conditions
            if (self.position.size > 0 and 
                (ma_ratio < 1 - gap or 
                 (len(self.rsi) > 0 and not pd.isna(self.rsi[-1]) and self.rsi[-1] > 75))):
                self.position.close()
                
            # Exit short conditions
            elif (self.position.size < 0 and 
                  (ma_ratio > 1 + gap or 
                   (len(self.rsi) > 0 and not pd.isna(self.rsi[-1]) and self.rsi[-1] < 25))):
                self.position.close()


def run_strategy_v3(timeframe='15m', months=None, optimize=True):
    """Run the technical indicators strategy"""
    if months is None:
        months = ['2024-01', '2024-02', '2024-03', '2024-04', 
                 '2024-05', '2024-06', '2024-07', '2024-08']
    
    print(f"Loading {timeframe} data for Technical Indicators Strategy...")
    data = load_doge_data([timeframe], months, add_indicators=False)
    df = data[timeframe]
    
    print(f"Data loaded: {len(df)} records from {df.index.min()} to {df.index.max()}")
    
    # Run backtest
    bt = Backtest(df, TechnicalIndicatorsStrategy, cash=100, commission=0.0003)
    
    print("\n=== Technical Indicators Strategy - Default Parameters ===")
    stats = bt.run()
    print(stats)
    
    if optimize:
        print("\n=== Running Parameter Optimization ===")
        
        stats_opt = bt.optimize(
            n1=range(15, 30, 5),
            n2=range(40, 70, 10),
            ma_type=['SMA', 'EMA'],
            base_gap=[0.0005, 0.001, 0.002],
            volatility_multiplier=[1.0, 1.5, 2.0],
            rsi_period=[10, 14, 18],
            bb_std=[1.5, 2.0, 2.5],
            base_position_size=[0.7, 0.8, 0.9],
            atr_multiplier=[1.5, 2.0, 2.5],
            maximize='Sharpe Ratio',
            constraint=lambda p: p.n1 < p.n2,
            max_tries=100
        )
        
        print("\n=== Optimized Results ===")
        print(stats_opt)
        print(f"\nOptimal parameters: {stats_opt['_strategy']}")
        
        return stats_opt
    
    return stats


if __name__ == "__main__":
    results = run_strategy_v3(timeframe='15m', optimize=True)
