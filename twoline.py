from backtesting import Backtest, Strategy
from backtesting.lib import crossover
from backtesting.lib import SignalStrategy, TrailingStrategy, resample_apply
from backtesting.test import SMA, GOOG
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import talib
import warnings
import backtesting
import multiprocessing
warnings.simplefilter(action='ignore', category=FutureWarning)

try:
    multiprocessing.set_start_method('fork', force=True)
except RuntimeError:
    # 如果启动方法已经设置，则忽略错误
    pass

def load_data(i, head=100000, tail=100000, month=['2025-01']):
    dfs = []
    for m in month:
        path = f'/Users/<USER>/workspace/data/DOGE/DOGE_{m}.parquet'
        df = pd.read_parquet(path)
        dfs.append(df)
    df = pd.concat(dfs)
    df = df[df['i'] == i]
    df = df.sort_values(by='z', ascending=True)
    df = df.drop_duplicates(subset=['z'])
    df = df.head(head).tail(tail)
    df['datetime'] = pd.to_datetime(df['z'])
    df = df.set_index('datetime')
    df = df[['o', 'h', 'l', 'c', 'v', 'q']]
    # 重命名列
    df = df.rename(columns={
        'o': 'Open',
        'h': 'High',
        'l': 'Low',
        'c': 'Close',
        'v': 'Volume',
        'q': 'Qty'
    })
    return df

def EMA(data, period):
    # return talib.EMA(data, timeperiod=period)
    return pd.Series(data).ewm(span=period, adjust=False).mean()

def RSI(array, n):
    gain = pd.Series(array).diff()
    loss = gain.copy()
    gain[gain < 0] = 0
    loss[loss > 0] = 0
    rs = gain.ewm(n).mean() / loss.abs().ewm(n).mean()
    return 100 - 100 / (1 + rs)

def volatility(low, high, n):
    data = pd.DataFrame({'low': low, 'high': high})
    data['volatility'] = data['high'] - data['low']
    return data['volatility'].rolling(window=n).std()

def ACC(array, n):
    return pd.Series(array).diff(n).abs() / pd.Series(array)


class TwoLine(TrailingStrategy):
    n1 = 21
    n2 = 76

    n1_30m = 7
    n2_30m = 20
    gap = 0.001

    def init(self):
        super().init()
        self.ema_fast = self.I(talib.EMA, self.data.Close, self.n1)
        self.ema_slow = self.I(talib.SMA, self.data.Close, self.n2)

        self.ema_fast_30m = resample_apply('30Min', talib.EMA, self.data.Close, self.n1_30m)
        self.ema_slow_30m = resample_apply('30Min', talib.SMA, self.data.Close, self.n2_30m)

        self.rsi = self.I(talib.RSI, self.data.Close, 14)
        self.macd = self.I(talib.MACD, self.data.Close, 12, 26, 9)

        # self.hma = self.I(talib.HMA, self.data.Close, self.n1)
        self.kama = self.I(talib.KAMA, self.data.Close, self.n1)

        self.atr = self.I(talib.ATR, self.data.High, self.data.Low, self.data.Close, timeperiod=10)

        # self.adx = self.I(talib.ADX, self.data.High, self.data.Low, self.data.Close, timeperiod=14)
        # self.adx_fast = self.I(talib.EMA, self.adx, 14)
        # self.adx_slow = self.I(talib.EMA, self.adx, 30)

        self.set_atr_periods(10)
        self.set_trailing_sl(2)

        self.acc4 = self.I(ACC, self.ema_fast, 4)
        self.acc6 = self.I(ACC, self.ema_fast, 6)
        
    def next(self):
        super().next()

        if self.position.size <= 0 and self.ema_fast[-1] / self.ema_slow[-1] > 1 + self.gap:
            self.position.close()
            self.buy()
                
        if self.position.size >= 0 and self.ema_fast[-1] / self.ema_slow[-1] < 1 - self.gap:
            self.position.close()
            self.sell()


if __name__ == '__main__':
    i = '15m'
    head = 200000
    tail = 200000
    month = ['2024-01', '2024-02', '2024-03', '2024-04', '2024-05', '2024-06', '2024-07', '2024-08']
    df = load_data(i, head, tail, month=month)
    bt = Backtest(df, TwoLine, cash=100, commission=.0003)
    stats = bt.run()
    print(stats)
    # bt.plot(filename='twoline.html')

    trades = stats['_trades']
    trades.to_csv('~/Downloads/twoline.csv')

    stats = bt.optimize(n1=range(7, 40, 3),
                        n2=range(15, 80, 3),
                        maximize='Equity Final [$]',
                        constraint=lambda param: param.n1 < param.n2,
                        method='sambo',
                        max_tries=1)
    print(stats)
    print(stats['_strategy'])