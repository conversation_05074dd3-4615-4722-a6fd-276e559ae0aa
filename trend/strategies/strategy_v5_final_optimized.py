"""
Strategy Iteration 5: Final Optimized Strategy
Combining the best elements from all previous iterations:
- Enhanced dual MA from V1 (good returns)
- Technical indicators from V3 (signal quality)
- Risk management from V4 (drawdown protection)
- Optimized for target performance: >50% annual return, <15% max drawdown
"""

from backtesting import Backtest, Strategy
from backtesting.lib import crossover, TrailingStrategy
import pandas as pd
import numpy as np
import talib
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
from data.data_loader import load_doge_data


class FinalOptimizedStrategy(TrailingStrategy):
    """
    Final optimized strategy combining best elements from all iterations
    """
    
    # Moving average parameters (from V1 success)
    n1 = 25  # Fast MA
    n2 = 30  # Slow MA (close periods for responsiveness)
    ma_type = 'SMA'
    
    # Entry parameters
    base_gap = 0.0005  # Small gap for more opportunities
    volatility_multiplier = 2.0
    min_hold_periods = 1  # Quick exits allowed
    
    # Technical indicators (from V3)
    rsi_period = 14
    rsi_oversold = 25
    rsi_overbought = 75
    
    # Position sizing (balanced approach)
    base_position_size = 0.8  # Aggressive but not maximum
    max_position_size = 0.95
    
    # Risk management (from V4 but less conservative)
    profit_target = 0.08  # 8% profit target
    stop_loss_atr = 2.0   # 2 ATR stop loss
    
    # Market regime detection
    trend_lookback = 20
    volatility_threshold = 0.03
    
    def init(self):
        super().init()
        
        # Moving averages
        if self.ma_type == 'SMA':
            self.ma_fast = self.I(talib.SMA, self.data.Close, self.n1)
            self.ma_slow = self.I(talib.SMA, self.data.Close, self.n2)
        else:  # EMA
            self.ma_fast = self.I(talib.EMA, self.data.Close, self.n1)
            self.ma_slow = self.I(talib.EMA, self.data.Close, self.n2)
            
        # Technical indicators
        self.rsi = self.I(talib.RSI, self.data.Close, self.rsi_period)
        
        # MACD for momentum
        macd_line, macd_signal, macd_hist = talib.MACD(self.data.Close, 12, 26, 9)
        self.macd_hist = self.I(lambda: macd_hist)
        
        # ATR for stops and volatility
        self.atr = self.I(talib.ATR, self.data.High, self.data.Low, self.data.Close, 14)
        
        # Volatility for regime detection
        self.volatility = self.I(self._calculate_volatility, self.data.Close, 20)
        
        # Trend strength
        self.trend_strength = self.I(self._calculate_trend_strength, self.data.Close, self.trend_lookback)
        
        # Track position
        self.entry_bar = 0
        self.entry_price = 0
        
        # Set trailing stop
        self.set_atr_periods(14)
        self.set_trailing_sl(self.stop_loss_atr)
        
    def _calculate_volatility(self, close, period):
        """Calculate rolling volatility"""
        returns = pd.Series(close).pct_change()
        return returns.rolling(window=period).std() * np.sqrt(period)
    
    def _calculate_trend_strength(self, close, period):
        """Calculate trend strength using price momentum"""
        close_series = pd.Series(close)
        # Calculate slope of linear regression over period
        x = np.arange(period)
        slopes = []
        
        for i in range(period, len(close_series)):
            y = close_series.iloc[i-period:i].values
            if len(y) == period:
                slope = np.polyfit(x, y, 1)[0]
                slopes.append(slope / close_series.iloc[i])  # Normalize by price
            else:
                slopes.append(0)
        
        # Pad with zeros for initial values
        result = [0] * period + slopes
        return pd.Series(result, index=close_series.index)
    
    def _get_market_regime(self):
        """Detect market regime: trending vs ranging"""
        if len(self.volatility) == 0 or len(self.trend_strength) == 0:
            return 'neutral'
            
        current_vol = self.volatility[-1] if not pd.isna(self.volatility[-1]) else 0
        current_trend = abs(self.trend_strength[-1]) if not pd.isna(self.trend_strength[-1]) else 0
        
        # High volatility + strong trend = trending market
        if current_vol > self.volatility_threshold and current_trend > 0.001:
            return 'trending'
        # Low volatility = ranging market
        elif current_vol < self.volatility_threshold * 0.5:
            return 'ranging'
        else:
            return 'neutral'
    
    def _get_dynamic_gap(self):
        """Calculate dynamic gap based on volatility and market regime"""
        base = self.base_gap
        
        if len(self.volatility) == 0 or pd.isna(self.volatility[-1]):
            return base
            
        regime = self._get_market_regime()
        vol_adjustment = self.volatility[-1] * self.volatility_multiplier
        
        # Adjust gap based on market regime
        if regime == 'trending':
            # Smaller gap in trending markets for more entries
            gap = base + (vol_adjustment * 0.5)
        elif regime == 'ranging':
            # Larger gap in ranging markets to avoid whipsaws
            gap = base + (vol_adjustment * 1.5)
        else:
            gap = base + vol_adjustment
            
        return min(max(gap, base), 0.005)  # Cap between base and 0.5%
    
    def _get_signal_strength(self, direction):
        """Calculate signal strength combining multiple factors"""
        strength = 0.0
        
        # MA momentum (30% weight)
        if len(self.ma_fast) >= 2:
            ma_momentum = (self.ma_fast[-1] - self.ma_fast[-2]) / self.ma_fast[-2]
            if (direction > 0 and ma_momentum > 0) or (direction < 0 and ma_momentum < 0):
                strength += 0.3
        
        # RSI confirmation (25% weight)
        if len(self.rsi) > 0 and not pd.isna(self.rsi[-1]):
            rsi_val = self.rsi[-1]
            if direction > 0 and self.rsi_oversold < rsi_val < 65:
                strength += 0.25
            elif direction < 0 and 35 < rsi_val < self.rsi_overbought:
                strength += 0.25
        
        # MACD momentum (25% weight)
        if len(self.macd_hist) >= 2 and not pd.isna(self.macd_hist[-1]) and not pd.isna(self.macd_hist[-2]):
            macd_momentum = self.macd_hist[-1] - self.macd_hist[-2]
            if (direction > 0 and macd_momentum > 0) or (direction < 0 and macd_momentum < 0):
                strength += 0.25
        
        # Trend strength (20% weight)
        if len(self.trend_strength) > 0 and not pd.isna(self.trend_strength[-1]):
            trend_val = self.trend_strength[-1]
            if (direction > 0 and trend_val > 0) or (direction < 0 and trend_val < 0):
                strength += 0.2
        
        return min(strength, 1.0)
    
    def _get_position_size(self, signal_strength):
        """Calculate position size based on signal strength and market regime"""
        regime = self._get_market_regime()
        
        # Base size adjusted by signal strength
        size = self.base_position_size * signal_strength
        
        # Adjust for market regime
        if regime == 'trending':
            size *= 1.2  # Larger positions in trending markets
        elif regime == 'ranging':
            size *= 0.8  # Smaller positions in ranging markets
            
        return min(max(size, 0.1), self.max_position_size)
    
    def _should_exit_position(self):
        """Check exit conditions"""
        if self.position.size == 0 or self.entry_price == 0:
            return False
            
        current_price = self.data.Close[-1]
        
        # Profit target
        if self.position.size > 0:  # Long
            profit_pct = (current_price - self.entry_price) / self.entry_price
            if profit_pct >= self.profit_target:
                return True
        else:  # Short
            profit_pct = (self.entry_price - current_price) / self.entry_price
            if profit_pct >= self.profit_target:
                return True
        
        # RSI extreme levels
        if len(self.rsi) > 0 and not pd.isna(self.rsi[-1]):
            rsi_val = self.rsi[-1]
            if ((self.position.size > 0 and rsi_val > 80) or 
                (self.position.size < 0 and rsi_val < 20)):
                return True
        
        return False
    
    def _can_exit_position(self):
        """Check if minimum holding period has passed"""
        return len(self.data) - self.entry_bar >= self.min_hold_periods
    
    def next(self):
        super().next()
        
        # Skip if not enough data
        if len(self.ma_fast) < 2 or len(self.ma_slow) < 2:
            return
        
        # Check exit conditions first
        if self.position.size != 0 and self._can_exit_position() and self._should_exit_position():
            self.position.close()
            self.entry_price = 0
            return
        
        # Entry signals
        gap = self._get_dynamic_gap()
        ma_ratio = self.ma_fast[-1] / self.ma_slow[-1]
        
        # Long signal
        long_signal = ma_ratio > 1 + gap
        long_strength = self._get_signal_strength(1) if long_signal else 0
        
        # Short signal
        short_signal = ma_ratio < 1 - gap
        short_strength = self._get_signal_strength(-1) if short_signal else 0
        
        # Minimum signal strength (lower threshold for more opportunities)
        min_strength = 0.3
        
        # Entry logic
        if (self.position.size <= 0 and long_signal and long_strength >= min_strength):
            self.position.close()
            size = self._get_position_size(long_strength)
            self.buy(size=size)
            self.entry_bar = len(self.data)
            self.entry_price = self.data.Close[-1]
            
        elif (self.position.size >= 0 and short_signal and short_strength >= min_strength):
            self.position.close()
            size = self._get_position_size(short_strength)
            self.sell(size=size)
            self.entry_bar = len(self.data)
            self.entry_price = self.data.Close[-1]


def run_strategy_v5(timeframe='15m', months=None, optimize=True):
    """Run the final optimized strategy"""
    if months is None:
        months = ['2024-01', '2024-02', '2024-03', '2024-04', 
                 '2024-05', '2024-06', '2024-07', '2024-08']
    
    print(f"Loading {timeframe} data for Final Optimized Strategy...")
    data = load_doge_data([timeframe], months, add_indicators=False)
    df = data[timeframe]
    
    print(f"Data loaded: {len(df)} records from {df.index.min()} to {df.index.max()}")
    
    # Run backtest
    bt = Backtest(df, FinalOptimizedStrategy, cash=100, commission=0.0003)
    
    print("\n=== Final Optimized Strategy - Default Parameters ===")
    stats = bt.run()
    print(stats)
    
    if optimize:
        print("\n=== Running Parameter Optimization ===")
        
        stats_opt = bt.optimize(
            n1=range(20, 35, 5),
            n2=range(25, 50, 5),
            ma_type=['SMA', 'EMA'],
            base_gap=[0.0005, 0.001, 0.002],
            volatility_multiplier=[1.5, 2.0, 2.5],
            base_position_size=[0.7, 0.8, 0.9],
            profit_target=[0.06, 0.08, 0.10],
            stop_loss_atr=[1.5, 2.0, 2.5],
            rsi_oversold=[20, 25, 30],
            rsi_overbought=[70, 75, 80],
            maximize='Return [%]',  # Focus on returns to meet target
            constraint=lambda p: p.n1 < p.n2,
            max_tries=100
        )
        
        print("\n=== Optimized Results ===")
        print(stats_opt)
        print(f"\nOptimal parameters: {stats_opt['_strategy']}")
        
        return stats_opt
    
    return stats


if __name__ == "__main__":
    results = run_strategy_v5(timeframe='15m', optimize=True)
