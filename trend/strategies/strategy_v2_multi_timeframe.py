"""
Strategy Iteration 2: Multi-Timeframe Confirmation
Improvements over V1:
- Higher timeframe trend confirmation (1h, 4h)
- Trend strength measurement using ADX
- Position sizing based on trend alignment
- Multi-timeframe signal filtering
"""

from backtesting import Backtest, Strategy
from backtesting.lib import crossover, TrailingStrategy, resample_apply
import pandas as pd
import numpy as np
import talib
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
from data.data_loader import load_doge_data


class MultiTimeframeStrategy(TrailingStrategy):
    """
    Multi-timeframe strategy with trend confirmation
    """
    
    # Primary timeframe parameters (15m)
    n1_15m = 25  # Fast MA period
    n2_15m = 30  # Slow MA period
    
    # Higher timeframe parameters (1h)
    n1_1h = 20   # Fast MA period for 1h
    n2_1h = 50   # Slow MA period for 1h
    
    # Strategy parameters
    ma_type = 'SMA'
    base_gap = 0.0005
    volatility_multiplier = 2.0
    min_hold_periods = 2
    
    # Multi-timeframe parameters
    trend_strength_threshold = 25  # ADX threshold for trend strength
    position_size_base = 0.7       # Base position size
    position_size_trend = 0.95     # Position size when trend aligned
    
    def init(self):
        super().init()
        
        # Primary timeframe (15m) indicators
        if self.ma_type == 'SMA':
            self.ma_fast_15m = self.I(talib.SMA, self.data.Close, self.n1_15m)
            self.ma_slow_15m = self.I(talib.SMA, self.data.Close, self.n2_15m)
        else:  # EMA
            self.ma_fast_15m = self.I(talib.EMA, self.data.Close, self.n1_15m)
            self.ma_slow_15m = self.I(talib.EMA, self.data.Close, self.n2_15m)
            
        # Higher timeframe (1h) indicators using resample_apply
        if self.ma_type == 'SMA':
            self.ma_fast_1h = resample_apply('1H', talib.SMA, self.data.Close, self.n1_1h)
            self.ma_slow_1h = resample_apply('1H', talib.SMA, self.data.Close, self.n2_1h)
        else:  # EMA
            self.ma_fast_1h = resample_apply('1H', talib.EMA, self.data.Close, self.n1_1h)
            self.ma_slow_1h = resample_apply('1H', talib.EMA, self.data.Close, self.n2_1h)
            
        # Technical indicators
        self.rsi = self.I(talib.RSI, self.data.Close, 14)
        self.atr = self.I(talib.ATR, self.data.High, self.data.Low, self.data.Close, 14)
        
        # Trend strength indicators
        self.adx = self.I(talib.ADX, self.data.High, self.data.Low, self.data.Close, 14)
        # Simplified trend strength for 1h - use MA slope instead of ADX
        self.ma_slope_1h = self.I(self._calculate_ma_slope, self.ma_fast_1h, 5)
        
        # Volatility for dynamic gap
        self.volatility = self.I(self._calculate_volatility, self.data.Close, 20)
        
        # Track position entry
        self.entry_bar = 0
        
        # Set trailing stop
        self.set_atr_periods(14)
        self.set_trailing_sl(2.0)
        
    def _calculate_volatility(self, close, period):
        """Calculate rolling volatility"""
        returns = pd.Series(close).pct_change()
        return returns.rolling(window=period).std() * np.sqrt(period)

    def _calculate_ma_slope(self, ma_values, period):
        """Calculate slope of moving average"""
        ma_series = pd.Series(ma_values)
        return ma_series.diff(period) / ma_series.shift(period)
    
    def _get_dynamic_gap(self):
        """Calculate dynamic gap based on volatility"""
        if len(self.volatility) == 0 or pd.isna(self.volatility[-1]):
            return self.base_gap
        
        current_vol = self.volatility[-1]
        dynamic_gap = self.base_gap + (current_vol * self.volatility_multiplier)
        return min(max(dynamic_gap, self.base_gap), 0.01)
    
    def _get_trend_direction_15m(self):
        """Get trend direction from 15m timeframe"""
        if len(self.ma_fast_15m) == 0 or len(self.ma_slow_15m) == 0:
            return 0
        
        if pd.isna(self.ma_fast_15m[-1]) or pd.isna(self.ma_slow_15m[-1]):
            return 0
            
        if self.ma_fast_15m[-1] > self.ma_slow_15m[-1]:
            return 1  # Bullish
        elif self.ma_fast_15m[-1] < self.ma_slow_15m[-1]:
            return -1  # Bearish
        else:
            return 0  # Neutral
    
    def _get_trend_direction_1h(self):
        """Get trend direction from 1h timeframe"""
        if len(self.ma_fast_1h) == 0 or len(self.ma_slow_1h) == 0:
            return 0
        
        if pd.isna(self.ma_fast_1h[-1]) or pd.isna(self.ma_slow_1h[-1]):
            return 0
            
        if self.ma_fast_1h[-1] > self.ma_slow_1h[-1]:
            return 1  # Bullish
        elif self.ma_fast_1h[-1] < self.ma_slow_1h[-1]:
            return -1  # Bearish
        else:
            return 0  # Neutral
    
    def _get_trend_strength(self):
        """Get trend strength from ADX"""
        if len(self.adx) == 0 or pd.isna(self.adx[-1]):
            return 0
        return self.adx[-1]
    
    def _get_trend_strength_1h(self):
        """Get trend strength from 1h MA slope"""
        if len(self.ma_slope_1h) == 0 or pd.isna(self.ma_slope_1h[-1]):
            return 0
        return abs(self.ma_slope_1h[-1]) * 1000  # Scale to similar range as ADX
    
    def _is_trend_aligned(self, direction):
        """Check if trends are aligned across timeframes"""
        trend_15m = self._get_trend_direction_15m()
        trend_1h = self._get_trend_direction_1h()
        
        # Both timeframes should agree on direction
        if direction > 0:  # Looking for bullish alignment
            return trend_15m > 0 and trend_1h > 0
        else:  # Looking for bearish alignment
            return trend_15m < 0 and trend_1h < 0
    
    def _get_position_size(self, direction):
        """Calculate position size based on trend alignment"""
        if self._is_trend_aligned(direction):
            # Larger position when trends are aligned
            return self.position_size_trend
        else:
            # Smaller position when trends are not aligned
            return self.position_size_base
    
    def _can_exit_position(self):
        """Check if minimum holding period has passed"""
        return len(self.data) - self.entry_bar >= self.min_hold_periods
    
    def _signal_quality_score(self, direction):
        """Calculate signal quality score (0-1)"""
        score = 0.0
        
        # Trend alignment bonus
        if self._is_trend_aligned(direction):
            score += 0.4
        
        # Trend strength bonus
        trend_strength = self._get_trend_strength()
        if trend_strength > self.trend_strength_threshold:
            score += 0.3
            
        # 1h trend strength bonus
        trend_strength_1h = self._get_trend_strength_1h()
        if trend_strength_1h > self.trend_strength_threshold:
            score += 0.2
        
        # RSI filter
        if len(self.rsi) > 0 and not pd.isna(self.rsi[-1]):
            rsi_val = self.rsi[-1]
            if direction > 0 and 30 < rsi_val < 70:  # Good RSI for long
                score += 0.1
            elif direction < 0 and 30 < rsi_val < 70:  # Good RSI for short
                score += 0.1
        
        return min(score, 1.0)
    
    def next(self):
        super().next()
        
        # Skip if not enough data
        if (len(self.ma_fast_15m) < 2 or len(self.ma_slow_15m) < 2 or
            len(self.ma_fast_1h) == 0 or len(self.ma_slow_1h) == 0):
            return
        
        # Calculate signals
        gap = self._get_dynamic_gap()
        ma_ratio_15m = self.ma_fast_15m[-1] / self.ma_slow_15m[-1]
        
        # Long signal conditions
        long_signal = ma_ratio_15m > 1 + gap
        long_quality = self._signal_quality_score(1) if long_signal else 0
        
        # Short signal conditions
        short_signal = ma_ratio_15m < 1 - gap
        short_quality = self._signal_quality_score(-1) if short_signal else 0
        
        # Minimum quality threshold
        min_quality = 0.3
        
        # Entry logic
        if (self.position.size <= 0 and long_signal and long_quality >= min_quality):
            self.position.close()
            size = self._get_position_size(1)
            self.buy(size=size)
            self.entry_bar = len(self.data)
            
        elif (self.position.size >= 0 and short_signal and short_quality >= min_quality):
            self.position.close()
            size = self._get_position_size(-1)
            self.sell(size=size)
            self.entry_bar = len(self.data)
            
        # Exit logic with trend change
        elif self.position.size != 0 and self._can_exit_position():
            
            # Exit long on strong bearish signal or trend change
            if (self.position.size > 0 and 
                (ma_ratio_15m < 1 - gap * 1.5 or self._get_trend_direction_1h() < 0)):
                self.position.close()
                
            # Exit short on strong bullish signal or trend change
            elif (self.position.size < 0 and 
                  (ma_ratio_15m > 1 + gap * 1.5 or self._get_trend_direction_1h() > 0)):
                self.position.close()


def run_strategy_v2(timeframe='15m', months=None, optimize=True):
    """Run the multi-timeframe strategy"""
    if months is None:
        months = ['2024-01', '2024-02', '2024-03', '2024-04', 
                 '2024-05', '2024-06', '2024-07', '2024-08']
    
    print(f"Loading {timeframe} data for Multi-Timeframe Strategy...")
    data = load_doge_data([timeframe], months, add_indicators=False)
    df = data[timeframe]
    
    print(f"Data loaded: {len(df)} records from {df.index.min()} to {df.index.max()}")
    
    # Run backtest
    bt = Backtest(df, MultiTimeframeStrategy, cash=100, commission=0.0003)
    
    print("\n=== Multi-Timeframe Strategy - Default Parameters ===")
    stats = bt.run()
    print(stats)
    
    if optimize:
        print("\n=== Running Parameter Optimization ===")
        
        stats_opt = bt.optimize(
            n1_15m=range(15, 35, 5),
            n2_15m=range(25, 45, 5),
            n1_1h=range(15, 30, 5),
            n2_1h=range(40, 70, 10),
            ma_type=['SMA', 'EMA'],
            base_gap=[0.0005, 0.001, 0.002],
            trend_strength_threshold=[20, 25, 30],
            position_size_base=[0.6, 0.7, 0.8],
            position_size_trend=[0.85, 0.95, 1.0],
            maximize='Sharpe Ratio',
            constraint=lambda p: p.n1_15m < p.n2_15m and p.n1_1h < p.n2_1h,
            max_tries=150
        )
        
        print("\n=== Optimized Results ===")
        print(stats_opt)
        print(f"\nOptimal parameters: {stats_opt['_strategy']}")
        
        return stats_opt
    
    return stats


if __name__ == "__main__":
    results = run_strategy_v2(timeframe='15m', optimize=True)
